<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\Product;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Products resource for managing Freemius products.
 * 
 * Handles CRUD operations and specialized methods for product management.
 */
class Products extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'products';
    }
    
    protected function getEntityClass(): string
    {
        return Product::class;
    }
    
    /**
     * Get products by type (plugin, theme, etc.).
     */
    public function byType(string $type): self
    {
        return $this->where('type', $type);
    }
    
    /**
     * Get active products only.
     */
    public function active(): self
    {
        return $this->where('is_active', true);
    }
    
    /**
     * Get products by pricing model.
     */
    public function byPricingModel(string $model): self
    {
        return $this->where('pricing_model', $model);
    }
    
    /**
     * Get products created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get products created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Search products by title.
     */
    public function searchByTitle(string $title): self
    {
        return $this->where('title', 'like', '%' . $title . '%');
    }
    
    /**
     * Get product plans resource.
     */
    public function plans(int $productId): Plans
    {
        /** @var Plans $plans */
        $plans = $this->createChildResource(Plans::class);
        $plans->setProductScope($productId);
        return $plans;
    }
    
    /**
     * Get product addons (returns Products resource filtered by parent).
     */
    public function addons(int $productId): Products
    {
        /** @var Products $addons */
        $addons = $this->createChildResource(Products::class);
        $addons->setProductScope($productId);
        return $addons->where('parent_plugin_id', $productId);
    }
    
    /**
     * Get product users resource.
     */
    public function users(int $productId): Users
    {
        /** @var Users $users */
        $users = $this->createChildResource(Users::class);
        $users->setProductScope($productId);
        return $users;
    }
    
    /**
     * Get product licenses resource.
     */
    public function licenses(int $productId): Licenses
    {
        /** @var Licenses $licenses */
        $licenses = $this->createChildResource(Licenses::class);
        $licenses->setProductScope($productId);
        return $licenses;
    }
    
    /**
     * Get product installations resource.
     */
    public function installations(int $productId): Installations
    {
        /** @var Installations $installations */
        $installations = $this->createChildResource(Installations::class);
        $installations->setProductScope($productId);
        return $installations;
    }
    
    /**
     * Get product subscriptions resource.
     */
    public function subscriptions(int $productId): Subscriptions
    {
        /** @var Subscriptions $subscriptions */
        $subscriptions = $this->createChildResource(Subscriptions::class);
        $subscriptions->setProductScope($productId);
        return $subscriptions;
    }
    
    /**
     * Get product payments resource.
     */
    public function payments(int $productId): Payments
    {
        /** @var Payments $payments */
        $payments = $this->createChildResource(Payments::class);
        $payments->setProductScope($productId);
        return $payments;
    }
    
    /**
     * Get product carts resource.
     */
    public function carts(int $productId): Carts
    {
        /** @var Carts $carts */
        $carts = $this->createChildResource(Carts::class);
        $carts->setProductScope($productId);
        return $carts;
    }
    
    /**
     * Get product coupons resource.
     */
    public function coupons(int $productId): Coupons
    {
        /** @var Coupons $coupons */
        $coupons = $this->createChildResource(Coupons::class);
        $coupons->setProductScope($productId);
        return $coupons;
    }
    
    /**
     * Get product tags resource.
     */
    public function tags(int $productId): Tags
    {
        /** @var Tags $tags */
        $tags = $this->createChildResource(Tags::class);
        $tags->setProductScope($productId);
        return $tags;
    }
    
    /**
     * Get product trials resource.
     */
    public function trials(int $productId): Trials
    {
        /** @var Trials $trials */
        $trials = $this->createChildResource(Trials::class);
        $trials->setProductScope($productId);
        return $trials;
    }
    
    /**
     * Get product tags.
     */
    public function tags(int $productId): array
    {
        $endpoint = $this->buildEndpoint($productId) . '/tags';
        $response = $this->httpClient->get($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return $data['tags'] ?? [];
    }
    
    /**
     * Add tags to a product.
     */
    public function addTags(int $productId, array $tags): bool
    {
        $endpoint = $this->buildEndpoint($productId) . '/tags';
        $response = $this->httpClient->post($endpoint, ['tags' => $tags]);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Remove tags from a product.
     */
    public function removeTags(int $productId, array $tags): bool
    {
        $endpoint = $this->buildEndpoint($productId) . '/tags';
        $response = $this->httpClient->delete($endpoint, ['tags' => $tags]);
        
        return $response->getStatusCode() === 200;
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['title', 'type'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a product");
            }
        }
        
        // Validate product type
        $validTypes = ['plugin', 'theme', 'service', 'other'];
        if (!in_array($data['type'], $validTypes)) {
            throw new ValidationException("Invalid product type. Must be one of: " . implode(', ', $validTypes));
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate product type if provided
        if (isset($data['type'])) {
            $validTypes = ['plugin', 'theme', 'service', 'other'];
            if (!in_array($data['type'], $validTypes)) {
                throw new ValidationException("Invalid product type. Must be one of: " . implode(', ', $validTypes));
            }
        }
    }
}