<?php

declare(strict_types=1);

namespace Freemius\SDK;

use InvalidArgumentException;
use Freemius\SDK\Http\HttpClient;
use Freemius\SDK\Resources\Products;
use Freemius\SDK\Resources\Users;
use Freemius\SDK\Resources\Licenses;
use Freemius\SDK\Resources\Installations;
use Freemius\SDK\Resources\Subscriptions;

/**
 * Main Freemius SDK facade
 * 
 * Provides the primary entry point for all SDK functionality,
 * including configuration management and resource access.
 */
class FreemiusSDK
{
    private Configuration $config;
    private Logger $logger;
    private ?HttpClient $httpClient = null;
    private ?Products $products = null;
    private ?Users $users = null;
    private ?Licenses $licenses = null;
    private ?Installations $installations = null;
    private ?Subscriptions $subscriptions = null;

    /**
     * Create a new Freemius SDK instance
     *
     * @param Configuration $config SDK configuration
     * @param Logger|null $logger Optional custom logger
     */
    public function __construct(Configuration $config, ?Logger $logger = null)
    {
        $this->config = $config;
        $this->logger = $logger ?? new Logger($config);
    }



    /**
     * Create SDK instance with bearer token
     *
     * @param string $bearerToken Freemius API bearer token
     * @param array $options Additional configuration options
     * @return self
     */
    public static function create(string $bearerToken, array $options = []): self
    {
        $options['bearerToken'] = $bearerToken;
        $config = new Configuration($options);
        return new self($config);
    }

    /**
     * Set product scope for API requests
     *
     * @param int $productId Product ID to scope requests to
     * @return self
     * @throws InvalidArgumentException If product ID is invalid
     */
    public function setProductScope(int $productId): self
    {
        $this->config->setProductScope($productId);
        
        // Reset resource instances to apply new scope
        $this->resetResourceInstances();
        
        return $this;
    }

    /**
     * Clear product scope for API requests
     *
     * @return self
     */
    public function clearProductScope(): self
    {
        $this->config->clearProductScope();
        
        // Reset resource instances to clear scope
        $this->resetResourceInstances();
        
        return $this;
    }

    /**
     * Get current product scope
     *
     * @return int|null
     */
    public function getProductScope(): ?int
    {
        return $this->config->getProductScope();
    }

    /**
     * Check if product scope is set
     *
     * @return bool
     */
    public function hasProductScope(): bool
    {
        return $this->config->hasProductScope();
    }

    /**
     * Get the current configuration
     *
     * @return Configuration
     */
    public function getConfiguration(): Configuration
    {
        return $this->config;
    }

    /**
     * Get the logger instance
     *
     * @return Logger
     */
    public function getLogger(): Logger
    {
        return $this->logger;
    }

    /**
     * Enable sandbox mode
     *
     * @param bool $sandbox Whether to use sandbox mode
     * @return self
     */
    public function useSandbox(bool $sandbox = true): self
    {
        $this->config->useSandbox($sandbox);
        return $this;
    }

    /**
     * Enable logging with optional level
     *
     * @param string $level Log level
     * @return self
     */
    public function enableLogging(string $level = 'info'): self
    {
        $this->config->enableLogging($level);
        return $this;
    }

    /**
     * Disable logging
     *
     * @return self
     */
    public function disableLogging(): self
    {
        $this->config->disableLogging();
        return $this;
    }

    /**
     * Set request timeout
     *
     * @param int $seconds Timeout in seconds
     * @return self
     */
    public function setTimeout(int $seconds): self
    {
        $this->config->setTimeout($seconds);
        return $this;
    }

    /**
     * Get HTTP client instance
     *
     * @return HttpClient
     */
    private function getHttpClient(): HttpClient
    {
        if ($this->httpClient === null) {
            $this->httpClient = new HttpClient($this->config, $this->logger);
        }
        
        return $this->httpClient;
    }

    /**
     * Reset all resource instances to apply new configuration
     *
     * @return void
     */
    private function resetResourceInstances(): void
    {
        $this->products = null;
        $this->users = null;
        $this->licenses = null;
        $this->installations = null;
        $this->subscriptions = null;
    }

    /**
     * Get Products resource
     *
     * @return Products
     */
    public function products(): Products
    {
        if ($this->products === null) {
            $this->products = new Products($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->products->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->products;
    }

    /**
     * Get Users resource
     *
     * @return Users
     */
    public function users(): Users
    {
        if ($this->users === null) {
            $this->users = new Users($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->users->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->users;
    }

    /**
     * Get Licenses resource
     *
     * @return Licenses
     */
    public function licenses(): Licenses
    {
        if ($this->licenses === null) {
            $this->licenses = new Licenses($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->licenses->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->licenses;
    }

    /**
     * Get Installations resource
     *
     * @return Installations
     */
    public function installations(): Installations
    {
        if ($this->installations === null) {
            $this->installations = new Installations($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->installations->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->installations;
    }

    /**
     * Get Subscriptions resource
     *
     * @return Subscriptions
     */
    public function subscriptions(): Subscriptions
    {
        if ($this->subscriptions === null) {
            $this->subscriptions = new Subscriptions($this->getHttpClient());
            if ($this->config->getProductScope() !== null) {
                $this->subscriptions->setProductScope($this->config->getProductScope());
            }
        }
        
        return $this->subscriptions;
    }
}