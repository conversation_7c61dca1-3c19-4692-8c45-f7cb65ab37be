Create for the Freemius API a full and complete PHP SDK (PHP 8.0+)
he SDK contains <PERSON><PERSON> auth and must offer support for easy creation of a webhook.
The SDK must cover ALL operations offered by the API and.
----

Full compliance with Freemius terminology and specifications

Fully pure PHP 8.0+ code and architectures.

---

* All implementation must follow **production-grade coding, architecture, and design standards**
* Responses must be **commercial-level quality**
* Use `swagger-mcp` tools to read Freemius OpenAPI specifications for exact parameter and response structures of the Freemius API (the mcp is already configured).

----

* Technology Stack & Restrictions
- Pure PHP (no frameworks)
- Allowed PHP Libraries: GuzzleHttp, monolog/monolog, , phpunit/phpunit, symfony/var-dumper (only)
- Testing: No automated tests; manual verification only
- Architecture: Production-grade, commercial-ready, following world-class, composer best practices

---

Freemius API base URL:
- Production: https://api.freemius.com/v1/
- Mock Server (sandbox): https://docs.freemius.com/_mock/api/

Links official webhook docs:
- https://freemius.com/help/documentation/saas/events-webhooks/
- https://github.com/Freemius/freemius-webhook-listener (Example)
- https://freemius.com/blog/changelog/new-feature-added-authentication-support-for-custom-webhook-events/