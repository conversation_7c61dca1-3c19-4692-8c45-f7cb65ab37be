Let correct something right AWAY. This is a libray, and SDK never should an SDK in PHP should required a `.env` and or `ENVIRONEMENT` variable! Never. The dev should pass the needed elements ans params.


---- 

This is a project under development and not something that has already been deployed or anything, so there is no need to worry about data loss or data preservation.

Implement things that are really supported and relevent to the Freemius API.

`This is a modernized, unofficial PHP SDK for the Freemius platform that provides seamless integration with Freemius REST API - ONLY the REST API.`



This SDK is JUST to offer an easy tools to do ONLY thinks that the REST API offer to developer. Be very carefull about your reasoning since Freemius has it was to do things. Also the REST API offer `Tools` to the devs that use Freemius for licensing their `Product` (WP plugin, Theme, Addons) to build a SaaS or and reporting platform upon their products and users data outside the Freemius developer platform. This is usefull to integrate their products to other tools and or platforms.

--------

Common and general SDK standard and enginering recommendations and rules.

* **PSR-1: Basic Coding Standard** - Fundamental coding style rules including opening tags, file encoding, and namespace declarations

* **PSR-2: Coding Style Guide** - Comprehensive coding style standards for consistency across PHP projects

* **PSR-12: Extended Coding Style** - Updated and expanded coding style guide that supersedes PSR-2 with more detailed formatting rules

* **PSR-3: Logger Interface** - Standard logging interface for consistent error handling and debugging across SDK components

* **PSR-4: Autoloader Standard** - Namespace-to-file mapping standard for automatic class loading, essential for SDK structure

* **PSR-6: Caching Interface** - Standard caching interface for implementing response caching and rate limiting features

* **PSR-7: HTTP Message Interface** - Standard HTTP request/response objects for consistent API communication handling

* **PSR-17: HTTP Factory Interface** - Factory interface for creating PSR-7 HTTP objects, crucial for SDK interoperability

* **PSR-18: HTTP Client Interface** - Standard HTTP client interface allowing users to plug in their preferred HTTP client library

* **composer.json Structure** - Must include proper PHP version constraints (^8.0 || ^7.4 or later), dependencies, and autoload configuration

* **Semantic Versioning** - Follow semver (major.minor.patch) for version numbering to ensure predictable updates

* **License Declaration** - Include appropriate open-source license (MIT, Apache 2.0, etc.) in composer.json

* **PSR-4 Autoloading** - Organize classes under proper namespace structure with PSR-4 autoloading configuration

* **Dependency Management** - Ensure proper HTTP client discovery and allow manual client installation when needed

* **Package Naming** - Follow vendor/package naming convention (e.g., yourcompany/api-sdk)

## **Authentication & Security Standards**

* **Bearer Token Implementation** - Implement secure token storage and automatic header injection for API requests

* **Token Refresh Logic** - Handle token expiration and automatic renewal when supported by the API

* **Secure Credential Storage** - Support multiple authentication methods and secure credential management

* **Rate Limiting** - Implement request throttling and respect API rate limits with proper retry mechanisms

## **SDK Architecture & Design Patterns**

* **Client Factory Pattern** - Provide simple factory methods for SDK initialization and configuration

* **Fluent Interface** - Implement method chaining for intuitive API usage and better developer experience

* **Resource-Based Organization** - Structure SDK methods around API resources (users, orders, etc.) for logical grouping

* **Error Handling** - Implement comprehensive exception hierarchy with specific error types for different API responses

* **Configuration Management** - Provide flexible configuration options for base URLs, timeouts, and custom headers

* **Response Object Mapping** - Convert API responses to PHP objects with proper type hints and validation

* **Standard Webhooks Compliance** - Follow Standard Webhooks guidelines for security, reliability, and consistency

* **Signature Verification** - Implement webhook payload signature validation for security

* **Event Type Handling** - Provide structured event handling with type-safe event objects

* **Retry Mechanism** - Handle failed webhook deliveries with exponential backoff retry logic

* **Payload Validation** - Validate incoming webhook payloads against expected schemas

* **Async Processing** - Support asynchronous webhook processing for better performance

* **Mock Support** - Include HTTP client mocking capabilities for testing without API calls

* **README Documentation** - Comprehensive installation, configuration, and usage examples

* **API Reference** - Complete method documentation with parameter descriptions and return types

* **Code Examples** - Practical usage examples for common SDK operations and webhook handling

* **IDE Support** - Proper PHPDoc annotations and type hints for better IDE autocompletion
