<?php

declare(strict_types=1);

namespace Freemius\SDK\Http;

use <PERSON>mius\SDK\Configuration;
use <PERSON><PERSON>us\SDK\Logger;
use Freemius\SDK\Authentication\AuthenticationInterface;
use <PERSON>mius\SDK\Exceptions\FreemiusException;
use G<PERSON><PERSON><PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Exception;

/**
 * HTTP client wrapper with Guzzle integration
 * 
 * Provides HTTP client functionality with authentication,
 * logging, retry logic, and timeout configuration.
 */
class HttpClient implements HttpClientInterface
{
    private GuzzleClient $client;
    private Configuration $config;
    private Logger $logger;
    private ?AuthenticationInterface $auth;

    /**
     * Create a new HTTP client instance
     *
     * @param Configuration $config SDK configuration
     * @param Logger $logger Logger instance
     * @param AuthenticationInterface|null $auth Authentication handler
     */
    public function __construct(
        Configuration $config,
        Logger $logger,
        ?AuthenticationInterface $auth = null
    ) {
        $this->config = $config;
        $this->logger = $logger;
        $this->auth = $auth;
        $this->client = $this->createGuzzleClient();
    }

    /**
     * {@inheritdoc}
     */
    public function get(string $uri, array $params = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($params)) {
            $options['query'] = $params;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('GET', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function post(string $uri, array $data = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($data)) {
            $options['json'] = $data;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('POST', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function put(string $uri, array $data = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($data)) {
            $options['json'] = $data;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('PUT', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function delete(string $uri, array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('DELETE', $uri, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function patch(string $uri, array $data = [], array $headers = []): ResponseInterface
    {
        $options = [];
        
        if (!empty($data)) {
            $options['json'] = $data;
        }
        
        if (!empty($headers)) {
            $options['headers'] = $headers;
        }

        return $this->sendRequest('PATCH', $uri, $options);
    }

    /**
     * Set authentication handler
     *
     * @param AuthenticationInterface $auth Authentication handler
     * @return self
     */
    public function setAuth(AuthenticationInterface $auth): self
    {
        $this->auth = $auth;
        return $this;
    }

    /**
     * Send an HTTP request with authentication and logging
     *
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @param array $options Request options
     * @return ResponseInterface
     * @throws GuzzleException
     */
    private function sendRequest(string $method, string $uri, array $options = []): ResponseInterface
    {
        $startTime = microtime(true);
        
        try {
            // Create the request
            $request = new Request($method, $uri);
            
            // Apply authentication if available
            if ($this->auth !== null) {
                $request = $this->auth->authenticate($request);
                
                // Merge authentication headers with request options
                if ($request->getHeaders()) {
                    $options['headers'] = array_merge(
                        $options['headers'] ?? [],
                        $request->getHeaders()
                    );
                }
            }

            // Log the request
            $this->logger->logRequest($request, [
                'options' => $this->sanitizeOptionsForLogging($options)
            ]);

            // Send the request
            $response = $this->client->request($method, $uri, $options);
            
            // Calculate duration
            $duration = microtime(true) - $startTime;
            
            // Log the response
            $this->logger->logResponse($response, $duration);
            
            return $response;
            
        } catch (RequestException $e) {
            $duration = microtime(true) - $startTime;
            
            // Convert to SDK exception
            $sdkException = ErrorHandler::handleRequestException($e);
            
            // Log the error
            $this->logger->logError($sdkException, [
                'method' => $method,
                'uri' => $uri,
                'duration' => $duration,
                'options' => $this->sanitizeOptionsForLogging($options)
            ]);
            
            // If we have a response, log it too
            if ($e->hasResponse()) {
                $this->logger->logResponse($e->getResponse(), $duration);
            }
            
            throw $sdkException;
            
        } catch (Exception $e) {
            $duration = microtime(true) - $startTime;
            
            // Wrap unexpected errors in SDK exception
            $sdkException = new FreemiusException(
                'Unexpected error: ' . $e->getMessage(),
                $e->getCode(),
                $e,
                [
                    'method' => $method,
                    'uri' => $uri,
                    'duration' => $duration,
                    'options' => $this->sanitizeOptionsForLogging($options)
                ]
            );
            
            // Log unexpected errors
            $this->logger->logError($sdkException);
            
            throw $sdkException;
        }
    }

    /**
     * Create and configure the Guzzle HTTP client
     *
     * @return GuzzleClient
     */
    private function createGuzzleClient(): GuzzleClient
    {
        $stack = HandlerStack::create();
        
        // Add retry middleware
        if ($this->config->getRetryAttempts() > 0) {
            $stack->push(Middleware::retry(
                $this->createRetryDecider(),
                $this->createRetryDelay()
            ));
        }

        $options = [
            'base_uri' => $this->config->getBaseUrl(),
            'timeout' => $this->config->getTimeout(),
            'connect_timeout' => min($this->config->getTimeout(), 10),
            'handler' => $stack,
            'headers' => [
                'User-Agent' => 'Freemius-PHP-SDK/1.0',
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'http_errors' => true, // Let Guzzle throw exceptions for HTTP errors
            'verify' => true, // Always verify SSL certificates
        ];

        return new GuzzleClient($options);
    }

    /**
     * Create retry decider function
     *
     * @return callable
     */
    private function createRetryDecider(): callable
    {
        return function (
            int $retries,
            RequestInterface $request,
            ?ResponseInterface $response = null,
            ?RequestException $exception = null
        ): bool {
            // Don't retry if we've exceeded max attempts
            if ($retries >= $this->config->getRetryAttempts()) {
                return false;
            }

            // Retry on connection errors
            if ($exception && !$exception->hasResponse()) {
                return true;
            }

            // Retry on server errors (5xx) and rate limiting (429)
            if ($response) {
                $statusCode = $response->getStatusCode();
                return $statusCode >= 500 || $statusCode === 429;
            }

            return false;
        };
    }

    /**
     * Create retry delay function
     *
     * @return callable
     */
    private function createRetryDelay(): callable
    {
        return function (int $retries, ?ResponseInterface $response = null): int {
            // If we have a Retry-After header, respect it
            if ($response && $response->hasHeader('Retry-After')) {
                $retryAfter = $response->getHeaderLine('Retry-After');
                
                // Handle both seconds and HTTP date formats
                if (is_numeric($retryAfter)) {
                    return (int) $retryAfter * 1000; // Convert to milliseconds
                }
                
                $retryTime = strtotime($retryAfter);
                if ($retryTime !== false) {
                    $delay = max(0, $retryTime - time());
                    return $delay * 1000; // Convert to milliseconds
                }
            }

            // Exponential backoff: 1s, 2s, 4s, 8s, etc.
            return (int) (pow(2, $retries) * 1000);
        };
    }

    /**
     * Sanitize request options for logging (remove sensitive data)
     *
     * @param array $options Request options
     * @return array Sanitized options
     */
    private function sanitizeOptionsForLogging(array $options): array
    {
        $sanitized = $options;
        
        // Remove sensitive headers
        if (isset($sanitized['headers'])) {
            $sensitiveHeaders = ['authorization', 'x-api-key', 'x-auth-token'];
            
            foreach ($sensitiveHeaders as $header) {
                if (isset($sanitized['headers'][$header])) {
                    $sanitized['headers'][$header] = '[REDACTED]';
                }
            }
        }
        
        // Remove sensitive body data
        if (isset($sanitized['json'])) {
            $sanitized['json'] = $this->redactSensitiveData($sanitized['json']);
        }
        
        if (isset($sanitized['form_params'])) {
            $sanitized['form_params'] = $this->redactSensitiveData($sanitized['form_params']);
        }

        return $sanitized;
    }

    /**
     * Redact sensitive data from arrays
     *
     * @param array $data Data to redact
     * @return array Redacted data
     */
    private function redactSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'bearer', 'api_key', 'access_token', 'refresh_token'
        ];
        
        $redacted = [];
        
        foreach ($data as $key => $value) {
            $lowerKey = strtolower((string) $key);
            
            if (in_array($lowerKey, $sensitiveFields, true)) {
                $redacted[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $redacted[$key] = $this->redactSensitiveData($value);
            } else {
                $redacted[$key] = $value;
            }
        }

        return $redacted;
    }
}