<?php

declare(strict_types=1);

namespace Freemius\SDK\Entities;

use DateTime;
use DateTimeInterface;
use InvalidArgumentException;
use Freemius\SDK\Support\DateTimeHelper;

/**
 * Abstract base class for all Freemius entities.
 * 
 * Provides common functionality for attribute management, data validation,
 * and type casting for all entity classes in the SDK.
 */
abstract class AbstractEntity
{
    /**
     * Entity attributes storage.
     */
    protected array $attributes = [];

    /**
     * Original attributes as received from API.
     */
    protected array $original = [];

    /**
     * Attributes that should be cast to specific types.
     */
    protected array $casts = [];

    /**
     * Attributes that should be treated as dates.
     */
    protected array $dates = [];

    /**
     * Create a new entity instance.
     */
    public function __construct(array $data = [])
    {
        $this->fill($data);
        $this->original = $this->attributes;
    }

    /**
     * Fill the entity with an array of attributes.
     */
    public function fill(array $attributes): self
    {
        foreach ($attributes as $key => $value) {
            $this->setAttribute($key, $value);
        }

        return $this;
    }

    /**
     * Get an attribute value.
     */
    public function getAttribute(string $key, mixed $default = null): mixed
    {
        if (!array_key_exists($key, $this->attributes)) {
            return $default;
        }

        $value = $this->attributes[$key];

        // Apply casting
        if (isset($this->casts[$key])) {
            return $this->castAttribute($key, $value);
        }

        // Handle dates
        if (in_array($key, $this->dates)) {
            return $this->asDateTime($value);
        }

        return $value;
    }

    /**
     * Set an attribute value.
     */
    public function setAttribute(string $key, mixed $value): self
    {
        $this->attributes[$key] = $value;
        return $this;
    }

    /**
     * Check if an attribute exists.
     */
    public function hasAttribute(string $key): bool
    {
        return array_key_exists($key, $this->attributes);
    }

    /**
     * Get all attributes as an array.
     */
    public function getAttributes(): array
    {
        $attributes = [];
        
        foreach ($this->attributes as $key => $value) {
            $attributes[$key] = $this->getAttribute($key);
        }

        return $attributes;
    }

    /**
     * Convert the entity to an array.
     */
    public function toArray(): array
    {
        return $this->getAttributes();
    }

    /**
     * Convert the entity to JSON.
     */
    public function toJson(int $options = 0): string
    {
        $json = json_encode($this->toArray(), $options);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('Failed to encode entity to JSON: ' . json_last_error_msg());
        }

        return $json;
    }

    /**
     * Check if the entity has been modified since creation.
     */
    public function isDirty(?string $key = null): bool
    {
        if ($key !== null) {
            return isset($this->attributes[$key]) && 
                   (!isset($this->original[$key]) || $this->attributes[$key] !== $this->original[$key]);
        }

        return $this->attributes !== $this->original;
    }

    /**
     * Get the original attribute values.
     */
    public function getOriginal(?string $key = null, mixed $default = null): mixed
    {
        if ($key === null) {
            return $this->original;
        }

        return $this->original[$key] ?? $default;
    }

    /**
     * Cast an attribute to a specific type.
     */
    protected function castAttribute(string $key, mixed $value): mixed
    {
        if ($value === null) {
            return null;
        }

        $castType = $this->casts[$key];

        return match ($castType) {
            'int', 'integer' => (int) $value,
            'float', 'double' => (float) $value,
            'string' => (string) $value,
            'bool', 'boolean' => $this->castToBoolean($value),
            'array' => is_array($value) ? $value : json_decode($value, true),
            'object' => is_object($value) ? $value : json_decode($value),
            'datetime' => $this->asDateTime($value),
            default => $value,
        };
    }

    /**
     * Convert a value to a DateTime instance using Freemius timestamp formats.
     */
    protected function asDateTime(mixed $value): ?DateTime
    {
        return DateTimeHelper::parse($value);
    }

    /**
     * Cast a value to boolean with proper string handling.
     */
    protected function castToBoolean(mixed $value): bool
    {
        if (is_bool($value)) {
            return $value;
        }
        
        if (is_string($value)) {
            $lower = strtolower(trim($value));
            if (in_array($lower, ['true', '1', 'yes', 'on'])) {
                return true;
            }
            if (in_array($lower, ['false', '0', 'no', 'off', ''])) {
                return false;
            }
        }
        
        return (bool) $value;
    }

    /**
     * Dynamically retrieve attributes.
     */
    public function __get(string $key): mixed
    {
        return $this->getAttribute($key);
    }

    /**
     * Dynamically set attributes.
     */
    public function __set(string $key, mixed $value): void
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Check if an attribute is set.
     */
    public function __isset(string $key): bool
    {
        return $this->hasAttribute($key);
    }

    /**
     * Unset an attribute.
     */
    public function __unset(string $key): void
    {
        unset($this->attributes[$key]);
    }
}