<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Http\HttpClientInterface;
use Freemius\SDK\Support\Collection;
use Freemius\SDK\Support\QueryBuilder;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Abstract base class for all Freemius API resources.
 * 
 * Provides common functionality including method chaining, query building,
 * and CRUD operations for Freemius API endpoints.
 */
abstract class AbstractResource
{
    protected HttpClientInterface $httpClient;
    protected QueryBuilder $queryBuilder;
    protected ?int $productScope = null;
    
    public function __construct(HttpClientInterface $httpClient)
    {
        $this->httpClient = $httpClient;
        $this->queryBuilder = new QueryBuilder();
    }
    
    /**
     * Get the base endpoint path for this resource.
     */
    abstract protected function getEndpoint(): string;
    
    /**
     * Get the entity class name for this resource.
     */
    abstract protected function getEntityClass(): string;
    
    /**
     * Set the product scope for API requests.
     *
     * @param int|null $productId Product ID to scope requests to
     * @return self
     * @throws ValidationException If product ID is invalid
     */
    public function setProductScope(?int $productId): self
    {
        if ($productId !== null && $productId <= 0) {
            throw new ValidationException('Product scope must be a positive integer or null');
        }
        
        $this->productScope = $productId;
        return $this;
    }

    /**
     * Get the current product scope.
     *
     * @return int|null
     */
    public function getProductScope(): ?int
    {
        return $this->productScope;
    }

    /**
     * Check if product scope is set.
     *
     * @return bool
     */
    public function hasProductScope(): bool
    {
        return $this->productScope !== null;
    }

    /**
     * Clear the product scope.
     *
     * @return self
     */
    public function clearProductScope(): self
    {
        $this->productScope = null;
        return $this;
    }
    
    /**
     * Add a where condition to the query.
     */
    public function where(string $field, $operator, $value = null): self
    {
        // If only two arguments provided, assume equals operator
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->queryBuilder->where($field, $operator, $value);
        return $this;
    }
    
    /**
     * Set the limit for the query.
     */
    public function limit(int $count): self
    {
        $this->queryBuilder->limit($count);
        return $this;
    }
    
    /**
     * Set the offset for the query.
     */
    public function offset(int $offset): self
    {
        $this->queryBuilder->offset($offset);
        return $this;
    }
    
    /**
     * Order results by a field.
     */
    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->queryBuilder->orderBy($field, $direction);
        return $this;
    }
    
    /**
     * Get a single entity by ID.
     *
     * @param int $id Entity ID
     * @return mixed Entity instance
     */
    public function get(int $id)
    {
        $endpoint = $this->buildScopedEndpoint($id);
        $response = $this->httpClient->get($endpoint);
        
        $data = json_decode($response->getBody()->getContents(), true);
        $entityClass = $this->getEntityClass();
        
        return new $entityClass($data);
    }
    
    /**
     * Get all entities matching the current query.
     *
     * @return Collection
     */
    public function all(): Collection
    {
        $params = $this->queryBuilder->toArray();
        $endpoint = $this->buildScopedEndpoint(null, $params);
        
        $response = $this->httpClient->get($endpoint);
        $data = json_decode($response->getBody()->getContents(), true);
        
        return $this->createCollection($data);
    }
    
    /**
     * Get the first entity matching the current query.
     */
    public function first()
    {
        $this->limit(1);
        $collection = $this->all();
        
        return $collection->first();
    }
    
    /**
     * Create a new entity.
     *
     * @param array $data Entity data
     * @return mixed Entity instance
     */
    public function create(array $data)
    {
        $this->validateCreateData($data);
        
        // Automatically inject product_id if scope is set and not already present
        if ($this->productScope !== null && !isset($data['product_id'])) {
            $data['product_id'] = $this->productScope;
        }
        
        $endpoint = $this->buildEndpoint();
        $response = $this->httpClient->post($endpoint, $data);
        
        $responseData = json_decode($response->getBody()->getContents(), true);
        $entityClass = $this->getEntityClass();
        
        return new $entityClass($responseData);
    }
    
    /**
     * Update an existing entity.
     *
     * @param int $id Entity ID
     * @param array $data Updated entity data
     * @return mixed Entity instance
     */
    public function update(int $id, array $data)
    {
        $this->validateUpdateData($data);
        
        // Automatically inject product_id if scope is set and not already present
        if ($this->productScope !== null && !isset($data['product_id'])) {
            $data['product_id'] = $this->productScope;
        }
        
        $endpoint = $this->buildEndpoint($id);
        $response = $this->httpClient->put($endpoint, $data);
        
        $responseData = json_decode($response->getBody()->getContents(), true);
        $entityClass = $this->getEntityClass();
        
        return new $entityClass($responseData);
    }
    
    /**
     * Delete an entity.
     *
     * @param int $id Entity ID
     * @return bool True if deletion was successful
     */
    public function delete(int $id): bool
    {
        $endpoint = $this->buildScopedEndpoint($id);
        $response = $this->httpClient->delete($endpoint);
        
        return $response->getStatusCode() === 204;
    }
    
    /**
     * Build the full endpoint URL with automatic product scope injection.
     *
     * @param int|null $id Optional entity ID
     * @return string
     * @throws ValidationException If product scope is required but not set
     */
    protected function buildEndpoint(?int $id = null): string
    {
        $endpoint = $this->getEndpoint();
        
        // Add product scope if set
        if ($this->productScope !== null) {
            $endpoint = "products/{$this->productScope}/" . ltrim($endpoint, '/');
        }
        
        // Add ID if provided
        if ($id !== null) {
            $endpoint = rtrim($endpoint, '/') . '/' . $id;
        }
        
        return $endpoint;
    }

    /**
     * Build endpoint with automatic product ID injection for scoped requests.
     *
     * @param int|null $id Optional entity ID
     * @param array $params Additional parameters to inject
     * @return string
     */
    protected function buildScopedEndpoint(?int $id = null, array $params = []): string
    {
        $endpoint = $this->buildEndpoint($id);
        
        // Automatically inject product_id parameter if scope is set and not already present
        if ($this->productScope !== null && !isset($params['product_id'])) {
            $params['product_id'] = $this->productScope;
        }
        
        // Append query parameters if any
        if (!empty($params)) {
            $queryString = http_build_query($params);
            $endpoint .= (strpos($endpoint, '?') !== false ? '&' : '?') . $queryString;
        }
        
        return $endpoint;
    }

    /**
     * Validate that product scope is set when required.
     *
     * @param bool $required Whether product scope is required
     * @throws ValidationException If product scope is required but not set
     */
    protected function validateProductScope(bool $required = false): void
    {
        if ($required && $this->productScope === null) {
            throw new ValidationException(
                'Product scope is required for this operation. Use setProductScope() to set a product ID.'
            );
        }
    }
    
    /**
     * Create a collection from API response data.
     */
    protected function createCollection(array $data): Collection
    {
        $entityClass = $this->getEntityClass();
        $items = [];
        
        // Handle different response formats
        if (isset($data['data'])) {
            $items = $data['data'];
        } elseif (is_array($data) && !empty($data)) {
            $items = $data;
        }
        
        $entities = array_map(function ($item) use ($entityClass) {
            return new $entityClass($item);
        }, $items);
        
        // Extract pagination metadata if available
        $pagination = $data['pagination'] ?? [];
        
        return new Collection($entities, $pagination);
    }
    
    /**
     * Validate data for create operations.
     * Override in child classes for specific validation.
     */
    protected function validateCreateData(array $data): void
    {
        // Base validation - override in child classes
    }
    
    /**
     * Validate data for update operations.
     * Override in child classes for specific validation.
     */
    protected function validateUpdateData(array $data): void
    {
        // Base validation - override in child classes
    }
    
    /**
     * Reset the query builder for a fresh query.
     */
    public function fresh(): self
    {
        $this->queryBuilder = new QueryBuilder();
        return $this;
    }
    
    /**
     * Create a child resource instance with inherited context.
     */
    protected function createChildResource(string $resourceClass): AbstractResource
    {
        $resource = new $resourceClass($this->httpClient);
        
        // Inherit product scope if set
        if ($this->productScope !== null) {
            $resource->setProductScope($this->productScope);
        }
        
        return $resource;
    }
}