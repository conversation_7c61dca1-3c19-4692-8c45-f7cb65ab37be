<?php

declare(strict_types=1);

namespace Freemius\SDK;

use InvalidArgumentException;

/**
 * Configuration management for the Freemius SDK
 * 
 * Handles all configuration options including API credentials,
 * environment settings, timeouts, and logging preferences.
 */
class Configuration
{
    private string $bearerToken;
    private string $baseUrl;
    private bool $sandbox;
    private int $timeout;
    private int $retryAttempts;
    private bool $logging;
    private string $logLevel;
    private ?int $productScope;

    /**
     * Default configuration values
     */
    private const DEFAULTS = [
        'baseUrl' => 'https://api.freemius.com/v1/',
        'sandboxUrl' => 'https://docs.freemius.com/_mock/api/',
        'sandbox' => false,
        'timeout' => 30,
        'retryAttempts' => 3,
        'logging' => false,
        'logLevel' => 'info',
        'productScope' => null,
    ];

    /**
     * Valid log levels
     */
    private const VALID_LOG_LEVELS = ['debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency'];

    /**
     * Create a new configuration instance
     *
     * @param array $options Configuration options
     * @throws InvalidArgumentException If required options are missing or invalid
     */
    public function __construct(array $options = [])
    {
        $this->validateRequiredOptions($options);
        
        $this->bearerToken = $options['bearerToken'];
        $this->sandbox = $options['sandbox'] ?? self::DEFAULTS['sandbox'];
        $this->baseUrl = $this->sandbox ? self::DEFAULTS['sandboxUrl'] : ($options['baseUrl'] ?? self::DEFAULTS['baseUrl']);
        $this->timeout = $options['timeout'] ?? self::DEFAULTS['timeout'];
        $this->retryAttempts = $options['retryAttempts'] ?? self::DEFAULTS['retryAttempts'];
        $this->logging = $options['logging'] ?? self::DEFAULTS['logging'];
        $this->logLevel = $options['logLevel'] ?? self::DEFAULTS['logLevel'];
        $this->productScope = $options['productScope'] ?? self::DEFAULTS['productScope'];

        $this->validateConfiguration();
    }



    /**
     * Enable sandbox mode
     *
     * @param bool $sandbox Whether to use sandbox mode
     * @return self
     */
    public function useSandbox(bool $sandbox = true): self
    {
        $this->sandbox = $sandbox;
        $this->baseUrl = $sandbox ? self::DEFAULTS['sandboxUrl'] : self::DEFAULTS['baseUrl'];
        
        return $this;
    }

    /**
     * Set request timeout
     *
     * @param int $seconds Timeout in seconds
     * @return self
     * @throws InvalidArgumentException If timeout is invalid
     */
    public function setTimeout(int $seconds): self
    {
        if ($seconds <= 0) {
            throw new InvalidArgumentException('Timeout must be greater than 0');
        }
        
        $this->timeout = $seconds;
        
        return $this;
    }

    /**
     * Enable logging with optional level
     *
     * @param string $level Log level
     * @return self
     * @throws InvalidArgumentException If log level is invalid
     */
    public function enableLogging(string $level = 'info'): self
    {
        if (!in_array($level, self::VALID_LOG_LEVELS, true)) {
            throw new InvalidArgumentException(sprintf('Invalid log level "%s". Valid levels: %s', $level, implode(', ', self::VALID_LOG_LEVELS)));
        }
        
        $this->logging = true;
        $this->logLevel = $level;
        
        return $this;
    }

    /**
     * Disable logging
     *
     * @return self
     */
    public function disableLogging(): self
    {
        $this->logging = false;
        
        return $this;
    }

    /**
     * Set product scope
     *
     * @param int|null $productId Product ID to scope requests to
     * @return self
     * @throws InvalidArgumentException If product ID is invalid
     */
    public function setProductScope(?int $productId): self
    {
        if ($productId !== null && $productId <= 0) {
            throw new InvalidArgumentException('Product scope must be a positive integer or null');
        }
        
        $this->productScope = $productId;
        
        return $this;
    }

    /**
     * Clear product scope
     *
     * @return self
     */
    public function clearProductScope(): self
    {
        $this->productScope = null;
        
        return $this;
    }

    /**
     * Check if product scope is set
     *
     * @return bool
     */
    public function hasProductScope(): bool
    {
        return $this->productScope !== null;
    }

    // Getters
    public function getBearerToken(): string
    {
        return $this->bearerToken;
    }

    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function isSandbox(): bool
    {
        return $this->sandbox;
    }

    public function getTimeout(): int
    {
        return $this->timeout;
    }

    public function getRetryAttempts(): int
    {
        return $this->retryAttempts;
    }

    public function isLoggingEnabled(): bool
    {
        return $this->logging;
    }

    public function getLogLevel(): string
    {
        return $this->logLevel;
    }

    public function getProductScope(): ?int
    {
        return $this->productScope;
    }

    /**
     * Validate required configuration options
     *
     * @param array $options
     * @throws InvalidArgumentException
     */
    private function validateRequiredOptions(array $options): void
    {
        if (empty($options['bearerToken'])) {
            throw new InvalidArgumentException('Bearer token is required');
        }
    }

    /**
     * Validate configuration values
     *
     * @throws InvalidArgumentException
     */
    private function validateConfiguration(): void
    {
        if ($this->timeout <= 0) {
            throw new InvalidArgumentException('Timeout must be greater than 0');
        }

        if ($this->retryAttempts < 0) {
            throw new InvalidArgumentException('Retry attempts must be 0 or greater');
        }

        if (!in_array($this->logLevel, self::VALID_LOG_LEVELS, true)) {
            throw new InvalidArgumentException(sprintf('Invalid log level "%s"', $this->logLevel));
        }

        if ($this->productScope !== null && $this->productScope <= 0) {
            throw new InvalidArgumentException('Product scope must be a positive integer');
        }
    }
}