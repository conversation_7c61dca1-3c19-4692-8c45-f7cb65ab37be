<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

use ArrayAccess;
use Countable;
use Iterator;
use JsonSerializable;

/**
 * Collection class for handling paginated API results.
 * 
 * Provides array-like access to entities with pagination metadata.
 */
class Collection implements ArrayAccess, Countable, Iterator, JsonSerializable
{
    private array $items;
    private array $pagination;
    private int $position = 0;
    
    public function __construct(array $items = [], array $pagination = [])
    {
        $this->items = $items;
        $this->pagination = $pagination;
    }
    
    /**
     * Get all items in the collection.
     */
    public function all(): array
    {
        return $this->items;
    }
    
    /**
     * Get the first item in the collection.
     */
    public function first()
    {
        return $this->items[0] ?? null;
    }
    
    /**
     * Get the last item in the collection.
     */
    public function last()
    {
        return end($this->items) ?: null;
    }
    
    /**
     * Check if the collection is empty.
     */
    public function isEmpty(): bool
    {
        return empty($this->items);
    }
    
    /**
     * Check if the collection is not empty.
     */
    public function isNotEmpty(): bool
    {
        return !$this->isEmpty();
    }
    
    /**
     * Get an item by index.
     */
    public function get(int $index)
    {
        return $this->items[$index] ?? null;
    }
    
    /**
     * Filter the collection using a callback.
     */
    public function filter(callable $callback): self
    {
        $filtered = array_filter($this->items, $callback);
        return new static(array_values($filtered), $this->pagination);
    }
    
    /**
     * Map the collection using a callback.
     */
    public function map(callable $callback): self
    {
        $mapped = array_map($callback, $this->items);
        return new static($mapped, $this->pagination);
    }
    
    /**
     * Reduce the collection to a single value.
     */
    public function reduce(callable $callback, $initial = null)
    {
        return array_reduce($this->items, $callback, $initial);
    }
    
    /**
     * Get a slice of the collection.
     */
    public function slice(int $offset, ?int $length = null): self
    {
        $sliced = array_slice($this->items, $offset, $length);
        return new static($sliced, $this->pagination);
    }
    
    /**
     * Take the first n items.
     */
    public function take(int $count): self
    {
        return $this->slice(0, $count);
    }
    
    /**
     * Skip the first n items.
     */
    public function skip(int $count): self
    {
        return $this->slice($count);
    }
    
    /**
     * Convert the collection to an array.
     */
    public function toArray(): array
    {
        return array_map(function ($item) {
            return method_exists($item, 'toArray') ? $item->toArray() : $item;
        }, $this->items);
    }
    
    /**
     * Convert the collection to JSON.
     */
    public function toJson(int $options = 0): string
    {
        return json_encode($this->jsonSerialize(), $options);
    }
    
    /**
     * Get pagination metadata.
     */
    public function getPagination(): array
    {
        return $this->pagination;
    }
    
    /**
     * Get the current page number.
     */
    public function getCurrentPage(): ?int
    {
        return $this->pagination['current_page'] ?? null;
    }
    
    /**
     * Get the total number of pages.
     */
    public function getTotalPages(): ?int
    {
        return $this->pagination['total_pages'] ?? null;
    }
    
    /**
     * Get the total number of items across all pages.
     */
    public function getTotal(): ?int
    {
        return $this->pagination['total'] ?? null;
    }
    
    /**
     * Get the number of items per page.
     */
    public function getPerPage(): ?int
    {
        return $this->pagination['per_page'] ?? null;
    }
    
    /**
     * Check if there is a next page.
     */
    public function hasNextPage(): bool
    {
        $currentPage = $this->getCurrentPage();
        $totalPages = $this->getTotalPages();
        
        return $currentPage !== null && $totalPages !== null && $currentPage < $totalPages;
    }
    
    /**
     * Check if there is a previous page.
     */
    public function hasPreviousPage(): bool
    {
        $currentPage = $this->getCurrentPage();
        
        return $currentPage !== null && $currentPage > 1;
    }
    
    // ArrayAccess implementation
    public function offsetExists($offset): bool
    {
        return isset($this->items[$offset]);
    }
    
    public function offsetGet($offset)
    {
        return $this->items[$offset] ?? null;
    }
    
    public function offsetSet($offset, $value): void
    {
        if ($offset === null) {
            $this->items[] = $value;
        } else {
            $this->items[$offset] = $value;
        }
    }
    
    public function offsetUnset($offset): void
    {
        unset($this->items[$offset]);
    }
    
    // Countable implementation
    public function count(): int
    {
        return count($this->items);
    }
    
    // Iterator implementation
    public function current()
    {
        return $this->items[$this->position];
    }
    
    public function key(): int
    {
        return $this->position;
    }
    
    public function next(): void
    {
        $this->position++;
    }
    
    public function rewind(): void
    {
        $this->position = 0;
    }
    
    public function valid(): bool
    {
        return isset($this->items[$this->position]);
    }
    
    // JsonSerializable implementation
    public function jsonSerialize(): array
    {
        return [
            'data' => $this->toArray(),
            'pagination' => $this->pagination,
        ];
    }
}