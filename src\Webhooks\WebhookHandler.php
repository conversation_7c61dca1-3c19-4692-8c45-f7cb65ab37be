<?php

declare(strict_types=1);

namespace <PERSON><PERSON>us\SDK\Webhooks;

use Freemius\SDK\Exceptions\WebhookException;
use Freemius\SDK\Webhooks\Events\WebhookEvent;
use Freemius\SDK\Webhooks\Events\InstallActivated;
use <PERSON>mius\SDK\Webhooks\Events\SubscriptionUpdated;
use Freemius\SDK\Webhooks\Events\PaymentCompleted;
use Freemius\SDK\Webhooks\Events\LicenseActivated;
use Freemius\SDK\Webhooks\Events\UserRegistered;
use Freemius\SDK\Webhooks\Events\TrialStarted;
use Freemius\SDK\Webhooks\Events\TrialEnded;
use Freemius\SDK\Webhooks\Events\UnknownEvent;

/**
 * Webhook handler for processing incoming Freemius webhooks
 * 
 * Handles webhook signature validation, event parsing, and creation of
 * structured event objects from webhook payloads.
 */
class WebhookHandler
{
    private WebhookValidator $validator;
    private array $eventMap;

    /**
     * Create a new webhook handler
     *
     * @param string $webhookSecret The webhook secret from <PERSON>mius
     * @param int $timestampTolerance Timestamp tolerance in seconds
     */
    public function __construct(string $webhookSecret, int $timestampTolerance = 300)
    {
        $this->validator = new WebhookValidator($webhookSecret, $timestampTolerance);
        $this->initializeEventMap();
    }

    /**
     * Handle incoming webhook request
     *
     * @param string $payload The raw webhook payload
     * @param array $headers HTTP headers from the webhook request
     * @return WebhookEvent The parsed webhook event
     * @throws WebhookException If validation or parsing fails
     */
    public function handle(string $payload, array $headers): WebhookEvent
    {
        // Extract signature and timestamp from headers
        $signature = WebhookValidator::extractSignature($headers);
        $timestamp = WebhookValidator::extractTimestamp($headers);

        // Validate the webhook
        $this->validator->validate($payload, $signature ?? '', $timestamp);

        // Parse the payload
        $data = $this->parsePayload($payload);

        // Create and return the appropriate event object
        return $this->createEvent($data);
    }

    /**
     * Handle webhook without signature validation (for testing)
     *
     * @param string $payload The raw webhook payload
     * @return WebhookEvent The parsed webhook event
     * @throws WebhookException If parsing fails
     */
    public function handleUnsafe(string $payload): WebhookEvent
    {
        $data = $this->parsePayload($payload);
        return $this->createEvent($data);
    }

    /**
     * Parse webhook payload JSON
     *
     * @param string $payload The raw webhook payload
     * @return array The parsed payload data
     * @throws WebhookException If payload is malformed
     */
    private function parsePayload(string $payload): array
    {
        if (empty($payload)) {
            throw WebhookException::malformedPayload($payload, 'Empty payload');
        }

        $data = json_decode($payload, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw WebhookException::malformedPayload(
                $payload,
                'JSON decode error: ' . json_last_error_msg()
            );
        }

        if (!is_array($data)) {
            throw WebhookException::malformedPayload($payload, 'Payload is not a JSON object');
        }

        return $data;
    }

    /**
     * Create appropriate event object from parsed data
     *
     * @param array $data The parsed webhook data
     * @return WebhookEvent The created event object
     * @throws WebhookException If event type is missing or unsupported
     */
    private function createEvent(array $data): WebhookEvent
    {
        $eventType = $data['type'] ?? $data['event'] ?? $data['event_type'] ?? null;

        if (empty($eventType)) {
            throw WebhookException::malformedPayload(
                json_encode($data),
                'Missing event type in webhook payload'
            );
        }

        $eventClass = $this->eventMap[$eventType] ?? null;

        if ($eventClass === null) {
            // Return UnknownEvent for unsupported event types instead of throwing
            return new UnknownEvent($data);
        }

        return new $eventClass($data);
    }

    /**
     * Initialize the event type to class mapping
     * Based on official Freemius webhook documentation
     */
    private function initializeEventMap(): void
    {
        $this->eventMap = [
            // Installation events
            'install.activated' => InstallActivated::class,
            'install.deactivated' => InstallActivated::class,
            'install.uninstalled' => InstallActivated::class,

            // Subscription events (note: Freemius uses "canceled" not "cancelled")
            'subscription.created' => SubscriptionUpdated::class,
            'subscription.updated' => SubscriptionUpdated::class,
            'subscription.canceled' => SubscriptionUpdated::class,
            'subscription.expired' => SubscriptionUpdated::class,
            'subscription.renewed' => SubscriptionUpdated::class,

            // Payment events
            'payment.completed' => PaymentCompleted::class,
            'payment.failed' => PaymentCompleted::class,
            'payment.refunded' => PaymentCompleted::class,
            'payment.chargeback' => PaymentCompleted::class,

            // License events
            'license.activated' => LicenseActivated::class,
            'license.deactivated' => LicenseActivated::class,
            'license.expired' => LicenseActivated::class,

            // User events
            'user.registered' => UserRegistered::class,
            'user.updated' => UserRegistered::class,
            'user.deleted' => UserRegistered::class,

            // Trial events (note: Freemius uses "canceled" not "cancelled")
            'trial.started' => TrialStarted::class,
            'trial.canceled' => TrialEnded::class,
            'trial.expired' => TrialEnded::class,
            'trial.converted' => TrialEnded::class,

            // Additional Freemius events based on documentation
            'addon.activated' => InstallActivated::class,
            'addon.deactivated' => InstallActivated::class,
            'addon.uninstalled' => InstallActivated::class,
            
            // Cart events
            'cart.abandoned' => UnknownEvent::class,
            'cart.recovered' => UnknownEvent::class,
            
            // Review events
            'review.created' => UnknownEvent::class,
            'review.updated' => UnknownEvent::class,
            
            // Affiliate events
            'affiliate.registered' => UnknownEvent::class,
            'affiliate.commission.created' => UnknownEvent::class,
        ];
    }

    /**
     * Get supported event types
     *
     * @return array List of supported event types
     */
    public function getSupportedEventTypes(): array
    {
        return array_keys($this->eventMap);
    }

    /**
     * Check if an event type is supported
     *
     * @param string $eventType The event type to check
     * @return bool True if supported
     */
    public function isEventTypeSupported(string $eventType): bool
    {
        return isset($this->eventMap[$eventType]);
    }

    /**
     * Add custom event type mapping
     *
     * @param string $eventType The event type
     * @param string $eventClass The event class (must extend WebhookEvent)
     * @return self
     */
    public function addEventType(string $eventType, string $eventClass): self
    {
        if (!is_subclass_of($eventClass, WebhookEvent::class)) {
            throw new \InvalidArgumentException(
                "Event class {$eventClass} must extend " . WebhookEvent::class
            );
        }

        $this->eventMap[$eventType] = $eventClass;
        return $this;
    }

    /**
     * Remove event type mapping
     *
     * @param string $eventType The event type to remove
     * @return self
     */
    public function removeEventType(string $eventType): self
    {
        unset($this->eventMap[$eventType]);
        return $this;
    }

    /**
     * Get the webhook validator instance
     *
     * @return WebhookValidator
     */
    public function getValidator(): WebhookValidator
    {
        return $this->validator;
    }

    /**
     * Validate webhook signature only
     *
     * @param string $payload The raw webhook payload
     * @param string $signature The signature from webhook headers
     * @return bool True if signature is valid
     * @throws WebhookException If validation fails
     */
    public function validateSignature(string $payload, string $signature): bool
    {
        return $this->validator->validateSignature($payload, $signature);
    }

    /**
     * Validate webhook timestamp only
     *
     * @param int $timestamp The timestamp from webhook headers
     * @return bool True if timestamp is valid
     * @throws WebhookException If validation fails
     */
    public function validateTimestamp(int $timestamp): bool
    {
        return $this->validator->validateTimestamp($timestamp);
    }

    /**
     * Create webhook handler from configuration
     *
     * @param array $config Configuration array with 'webhook_secret' and optional 'timestamp_tolerance'
     * @return self
     * @throws WebhookException If webhook secret is missing
     */
    public static function fromConfig(array $config): self
    {
        $webhookSecret = $config['webhook_secret'] ?? '';
        if (empty($webhookSecret)) {
            throw WebhookException::missingWebhookSecret();
        }

        $timestampTolerance = $config['timestamp_tolerance'] ?? 300;

        return new self($webhookSecret, $timestampTolerance);
    }
}