<?php

declare(strict_types=1);

namespace Freemius\SDK;

use Monolog\Logger as MonologLogger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;
use Monolog\Processor\PsrLogMessageProcessor;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Exception;
use InvalidArgumentException;

/**
 * Logging utilities for the Freemius SDK
 * 
 * Provides structured logging with sensitive data redaction,
 * request/response logging, and configurable output formats.
 */
class Logger
{
    private LoggerInterface $logger;
    private Configuration $config;

    /**
     * Sensitive headers that should be redacted in logs
     */
    private const SENSITIVE_HEADERS = [
        'authorization',
        'x-api-key',
        'x-auth-token',
        'cookie',
        'set-cookie',
    ];

    /**
     * Sensitive request/response body fields that should be redacted
     */
    private const SENSITIVE_FIELDS = [
        'password',
        'token',
        'secret',
        'key',
        'authorization',
        'bearer',
        'api_key',
        'access_token',
        'refresh_token',
    ];

    /**
     * Create a new logger instance
     *
     * @param Configuration $config SDK configuration
     * @param LoggerInterface|null $logger Optional custom logger instance
     */
    public function __construct(Configuration $config, ?LoggerInterface $logger = null)
    {
        $this->config = $config;
        $this->logger = $logger ?? $this->createDefaultLogger();
    }

    /**
     * Log an HTTP request
     *
     * @param RequestInterface $request The HTTP request
     * @param array $context Additional context
     */
    public function logRequest(RequestInterface $request, array $context = []): void
    {
        if (!$this->config->isLoggingEnabled()) {
            return;
        }

        $headers = $this->redactSensitiveHeaders($request->getHeaders());
        $body = $this->redactSensitiveData($request->getBody()->getContents());
        
        // Reset body stream position
        $request->getBody()->rewind();

        $logData = [
            'method' => $request->getMethod(),
            'uri' => (string) $request->getUri(),
            'headers' => $headers,
            'body' => $body,
            'context' => $context,
        ];

        $this->logger->info('Freemius API Request', $logData);
    }

    /**
     * Log an HTTP response
     *
     * @param ResponseInterface $response The HTTP response
     * @param float|null $duration Request duration in seconds
     * @param array $context Additional context
     */
    public function logResponse(ResponseInterface $response, ?float $duration = null, array $context = []): void
    {
        if (!$this->config->isLoggingEnabled()) {
            return;
        }

        $headers = $this->redactSensitiveHeaders($response->getHeaders());
        $body = $this->redactSensitiveData($response->getBody()->getContents());
        
        // Reset body stream position
        $response->getBody()->rewind();

        $logData = [
            'status_code' => $response->getStatusCode(),
            'reason_phrase' => $response->getReasonPhrase(),
            'headers' => $headers,
            'body' => $body,
            'duration' => $duration,
            'context' => $context,
        ];

        $level = $response->getStatusCode() >= 400 ? 'error' : 'info';
        $this->logger->log($level, 'Freemius API Response', $logData);
    }

    /**
     * Log an error or exception
     *
     * @param Exception $exception The exception to log
     * @param array $context Additional context
     */
    public function logError(Exception $exception, array $context = []): void
    {
        if (!$this->config->isLoggingEnabled()) {
            return;
        }

        $logData = [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context,
        ];

        $this->logger->error('Freemius SDK Error', $logData);
    }

    /**
     * Log a general message
     *
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context
     */
    public function log(string $level, string $message, array $context = []): void
    {
        if (!$this->config->isLoggingEnabled()) {
            return;
        }

        $this->logger->log($level, $message, $context);
    }

    /**
     * Get the underlying logger instance
     *
     * @return LoggerInterface
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    /**
     * Create the default Monolog logger
     *
     * @return LoggerInterface
     */
    private function createDefaultLogger(): LoggerInterface
    {
        $logger = new MonologLogger('freemius-sdk');
        
        // Add processor for PSR-3 message interpolation
        $logger->pushProcessor(new PsrLogMessageProcessor());

        // Determine log level
        $logLevel = $this->getMonologLevel($this->config->getLogLevel());

        // Create handler - use rotating file handler for production
        if (is_dir('logs') && is_writable('logs')) {
            $handler = new RotatingFileHandler('logs/freemius-sdk.log', 0, $logLevel);
        } else {
            // Fallback to stderr if logs directory is not available
            $handler = new StreamHandler('php://stderr', $logLevel);
        }

        // Set custom formatter
        $formatter = new LineFormatter(
            "[%datetime%] %channel%.%level_name%: %message% %context% %extra%\n",
            'Y-m-d H:i:s',
            true,
            true
        );
        $handler->setFormatter($formatter);

        $logger->pushHandler($handler);

        return $logger;
    }

    /**
     * Convert string log level to Monolog level constant
     *
     * @param string $level String log level
     * @return int Monolog level constant
     */
    private function getMonologLevel(string $level): int
    {
        $levels = [
            'debug' => MonologLogger::DEBUG,
            'info' => MonologLogger::INFO,
            'notice' => MonologLogger::NOTICE,
            'warning' => MonologLogger::WARNING,
            'error' => MonologLogger::ERROR,
            'critical' => MonologLogger::CRITICAL,
            'alert' => MonologLogger::ALERT,
            'emergency' => MonologLogger::EMERGENCY,
        ];

        return $levels[$level] ?? MonologLogger::INFO;
    }

    /**
     * Redact sensitive headers from HTTP headers array
     *
     * @param array $headers HTTP headers
     * @return array Redacted headers
     */
    private function redactSensitiveHeaders(array $headers): array
    {
        $redacted = [];
        
        foreach ($headers as $name => $values) {
            $lowerName = strtolower($name);
            
            if (in_array($lowerName, self::SENSITIVE_HEADERS, true)) {
                $redacted[$name] = ['[REDACTED]'];
            } else {
                $redacted[$name] = $values;
            }
        }

        return $redacted;
    }

    /**
     * Redact sensitive data from request/response body
     *
     * @param string $data Raw body data
     * @return string Redacted data
     */
    private function redactSensitiveData(string $data): string
    {
        if (empty($data)) {
            return $data;
        }

        // Try to decode as JSON
        $decoded = json_decode($data, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $redacted = $this->redactSensitiveArray($decoded);
            return json_encode($redacted, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        }

        // For non-JSON data, redact common patterns
        $patterns = [
            '/("(?:' . implode('|', self::SENSITIVE_FIELDS) . ')"\s*:\s*")[^"]*(")/i' => '$1[REDACTED]$2',
            '/((?:' . implode('|', self::SENSITIVE_FIELDS) . ')=)[^&\s]*/i' => '$1[REDACTED]',
        ];

        foreach ($patterns as $pattern => $replacement) {
            $data = preg_replace($pattern, $replacement, $data);
        }

        return $data;
    }

    /**
     * Recursively redact sensitive fields from an array
     *
     * @param array $data Data array
     * @return array Redacted array
     */
    private function redactSensitiveArray(array $data): array
    {
        $redacted = [];
        
        foreach ($data as $key => $value) {
            $lowerKey = strtolower((string) $key);
            
            if (in_array($lowerKey, self::SENSITIVE_FIELDS, true)) {
                $redacted[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $redacted[$key] = $this->redactSensitiveArray($value);
            } else {
                $redacted[$key] = $value;
            }
        }

        return $redacted;
    }
}