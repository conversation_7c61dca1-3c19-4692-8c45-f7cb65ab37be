<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\User;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Users resource for managing Freemius users.
 * 
 * Handles CRUD operations and specialized methods for user management.
 */
class Users extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'users';
    }
    
    protected function getEntityClass(): string
    {
        return User::class;
    }
    
    /**
     * Search users by email.
     */
    public function byEmail(string $email): self
    {
        return $this->where('email', $email);
    }
    
    /**
     * Search users by email pattern.
     */
    public function searchByEmail(string $pattern): self
    {
        return $this->where('email', 'like', '%' . $pattern . '%');
    }
    
    /**
     * Get users by verification status.
     */
    public function byVerificationStatus(bool $isVerified): self
    {
        return $this->where('is_verified', $isVerified);
    }
    
    /**
     * Get verified users only.
     */
    public function verified(): self
    {
        return $this->byVerificationStatus(true);
    }
    
    /**
     * Get unverified users only.
     */
    public function unverified(): self
    {
        return $this->byVerificationStatus(false);
    }
    
    /**
     * Get users registered after a specific date.
     */
    public function registeredAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get users registered before a specific date.
     */
    public function registeredBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get user installations resource.
     */
    public function installations(int $userId): Installations
    {
        /** @var Installations $installations */
        $installations = $this->createChildResource(Installations::class);
        return $installations->byUser($userId);
    }
    
    /**
     * Get user licenses resource.
     */
    public function licenses(int $userId): Licenses
    {
        /** @var Licenses $licenses */
        $licenses = $this->createChildResource(Licenses::class);
        return $licenses->byUser($userId);
    }
    
    /**
     * Get user subscriptions resource.
     */
    public function subscriptions(int $userId): Subscriptions
    {
        /** @var Subscriptions $subscriptions */
        $subscriptions = $this->createChildResource(Subscriptions::class);
        return $subscriptions->byUser($userId);
    }
    
    /**
     * Get user payments resource.
     */
    public function payments(int $userId): Payments
    {
        /** @var Payments $payments */
        $payments = $this->createChildResource(Payments::class);
        return $payments->byUser($userId);
    }
    
    /**
     * Get user carts resource.
     */
    public function carts(int $userId): Carts
    {
        /** @var Carts $carts */
        $carts = $this->createChildResource(Carts::class);
        return $carts->byUser($userId);
    }
    
    /**
     * Get user trials resource.
     */
    public function trials(int $userId): Trials
    {
        /** @var Trials $trials */
        $trials = $this->createChildResource(Trials::class);
        return $trials->byUser($userId);
    }
    
    /**
     * Verify a user's email.
     */
    public function verifyEmail(int $userId): bool
    {
        $endpoint = $this->buildEndpoint($userId) . '/verify';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Send verification email to user.
     */
    public function sendVerificationEmail(int $userId): bool
    {
        $endpoint = $this->buildEndpoint($userId) . '/send-verification';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Block a user.
     */
    public function block(int $userId): bool
    {
        $endpoint = $this->buildEndpoint($userId) . '/block';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Unblock a user.
     */
    public function unblock(int $userId): bool
    {
        $endpoint = $this->buildEndpoint($userId) . '/unblock';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['email'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a user");
            }
        }
        
        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new ValidationException("Invalid email format");
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate email format if provided
        if (isset($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new ValidationException("Invalid email format");
        }
    }
}