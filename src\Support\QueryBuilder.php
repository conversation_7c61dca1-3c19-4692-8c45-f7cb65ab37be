<?php

declare(strict_types=1);

namespace Freemius\SDK\Support;

/**
 * Query builder for constructing API request parameters.
 * 
 * Supports filtering, pagination, and ordering for Freemius API requests.
 */
class QueryBuilder
{
    private array $wheres = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $orderBy = [];
    
    /**
     * Add a where condition.
     */
    public function where(string $field, string $operator, $value): self
    {
        $this->wheres[] = [
            'field' => $field,
            'operator' => $operator,
            'value' => $value,
        ];
        
        return $this;
    }
    
    /**
     * Set the limit.
     */
    public function limit(int $count): self
    {
        $this->limit = $count;
        return $this;
    }
    
    /**
     * Set the offset.
     */
    public function offset(int $offset): self
    {
        $this->offset = $offset;
        return $this;
    }
    
    /**
     * Add an order by clause.
     */
    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->orderBy[] = [
            'field' => $field,
            'direction' => strtolower($direction),
        ];
        
        return $this;
    }
    
    /**
     * Convert the query to an array of parameters.
     */
    public function toArray(): array
    {
        $params = [];
        
        // Add where conditions
        foreach ($this->wheres as $where) {
            $key = $where['field'];
            
            // Handle different operators
            switch ($where['operator']) {
                case '=':
                case 'eq':
                    $params[$key] = $where['value'];
                    break;
                case '!=':
                case 'ne':
                    $params[$key . '[ne]'] = $where['value'];
                    break;
                case '>':
                case 'gt':
                    $params[$key . '[gt]'] = $where['value'];
                    break;
                case '>=':
                case 'gte':
                    $params[$key . '[gte]'] = $where['value'];
                    break;
                case '<':
                case 'lt':
                    $params[$key . '[lt]'] = $where['value'];
                    break;
                case '<=':
                case 'lte':
                    $params[$key . '[lte]'] = $where['value'];
                    break;
                case 'like':
                    $params[$key . '[like]'] = $where['value'];
                    break;
                case 'in':
                    if (is_array($where['value'])) {
                        $params[$key . '[in]'] = implode(',', $where['value']);
                    } else {
                        $params[$key . '[in]'] = $where['value'];
                    }
                    break;
                case 'not_in':
                    if (is_array($where['value'])) {
                        $params[$key . '[not_in]'] = implode(',', $where['value']);
                    } else {
                        $params[$key . '[not_in]'] = $where['value'];
                    }
                    break;
                default:
                    $params[$key] = $where['value'];
            }
        }
        
        // Add pagination
        if ($this->limit !== null) {
            $params['limit'] = $this->limit;
        }
        
        if ($this->offset !== null) {
            $params['offset'] = $this->offset;
        }
        
        // Add ordering
        if (!empty($this->orderBy)) {
            $orderStrings = [];
            foreach ($this->orderBy as $order) {
                $orderStrings[] = $order['field'] . ':' . $order['direction'];
            }
            $params['order'] = implode(',', $orderStrings);
        }
        
        return $params;
    }
    
    /**
     * Check if the query has any conditions.
     */
    public function hasConditions(): bool
    {
        return !empty($this->wheres) || 
               $this->limit !== null || 
               $this->offset !== null || 
               !empty($this->orderBy);
    }
    
    /**
     * Get the where conditions.
     */
    public function getWheres(): array
    {
        return $this->wheres;
    }
    
    /**
     * Get the limit.
     */
    public function getLimit(): ?int
    {
        return $this->limit;
    }
    
    /**
     * Get the offset.
     */
    public function getOffset(): ?int
    {
        return $this->offset;
    }
    
    /**
     * Get the order by clauses.
     */
    public function getOrderBy(): array
    {
        return $this->orderBy;
    }
}