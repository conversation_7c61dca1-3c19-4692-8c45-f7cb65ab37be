<?php

declare(strict_types=1);

namespace Freemius\SDK\Resources;

use Freemius\SDK\Entities\License;
use Freemius\SDK\Exceptions\ValidationException;

/**
 * Licenses resource for managing Freemius licenses.
 * 
 * Handles CRUD operations and specialized methods for license management.
 */
class Licenses extends AbstractResource
{
    protected function getEndpoint(): string
    {
        return 'licenses';
    }
    
    protected function getEntityClass(): string
    {
        return License::class;
    }
    
    /**
     * Get licenses by status.
     */
    public function byStatus(string $status): self
    {
        return $this->where('status', $status);
    }
    
    /**
     * Get active licenses only.
     */
    public function active(): self
    {
        return $this->byStatus('active');
    }
    
    /**
     * Get expired licenses only.
     */
    public function expired(): self
    {
        return $this->byStatus('expired');
    }
    
    /**
     * Get cancelled licenses only.
     */
    public function cancelled(): self
    {
        return $this->byStatus('cancelled');
    }
    
    /**
     * Get licenses by user ID.
     */
    public function byUser(int $userId): self
    {
        return $this->where('user_id', $userId);
    }
    
    /**
     * Get licenses by plan ID.
     */
    public function byPlan(int $planId): self
    {
        return $this->where('plan_id', $planId);
    }
    
    /**
     * Get licenses expiring before a specific date.
     */
    public function expiringBefore(string $date): self
    {
        return $this->where('expires', '<', $date);
    }
    
    /**
     * Get licenses expiring after a specific date.
     */
    public function expiringAfter(string $date): self
    {
        return $this->where('expires', '>', $date);
    }
    
    /**
     * Get licenses created after a specific date.
     */
    public function createdAfter(string $date): self
    {
        return $this->where('created', '>', $date);
    }
    
    /**
     * Get licenses created before a specific date.
     */
    public function createdBefore(string $date): self
    {
        return $this->where('created', '<', $date);
    }
    
    /**
     * Get license installations resource.
     */
    public function installations(int $licenseId): Installations
    {
        /** @var Installations $installations */
        $installations = $this->createChildResource(Installations::class);
        return $installations->byLicense($licenseId);
    }
    
    /**
     * Get license subscriptions resource.
     */
    public function subscriptions(int $licenseId): Subscriptions
    {
        /** @var Subscriptions $subscriptions */
        $subscriptions = $this->createChildResource(Subscriptions::class);
        return $subscriptions->where('license_id', $licenseId);
    }
    
    /**
     * Get license payments resource.
     */
    public function payments(int $licenseId): Payments
    {
        /** @var Payments $payments */
        $payments = $this->createChildResource(Payments::class);
        return $payments->byLicense($licenseId);
    }
    
    /**
     * Activate a license.
     */
    public function activate(int $licenseId, array $activationData = [])
    {
        $endpoint = $this->buildEndpoint($licenseId) . '/activate';
        $response = $this->httpClient->post($endpoint, $activationData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new License($data);
    }
    
    /**
     * Deactivate a license.
     */
    public function deactivate(int $licenseId): bool
    {
        $endpoint = $this->buildEndpoint($licenseId) . '/deactivate';
        $response = $this->httpClient->post($endpoint);
        
        return $response->getStatusCode() === 200;
    }
    
    /**
     * Extend a license.
     */
    public function extend(int $licenseId, int $days)
    {
        $endpoint = $this->buildEndpoint($licenseId) . '/extend';
        $response = $this->httpClient->post($endpoint, ['days' => $days]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new License($data);
    }
    
    /**
     * Cancel a license.
     */
    public function cancel(int $licenseId, array $cancellationData = [])
    {
        $endpoint = $this->buildEndpoint($licenseId) . '/cancel';
        $response = $this->httpClient->post($endpoint, $cancellationData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new License($data);
    }
    
    /**
     * Refund a license.
     */
    public function refund(int $licenseId, array $refundData = [])
    {
        $endpoint = $this->buildEndpoint($licenseId) . '/refund';
        $response = $this->httpClient->post($endpoint, $refundData);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return new License($data);
    }
    
    /**
     * Get license usage statistics.
     */
    public function usage(int $licenseId): array
    {
        $endpoint = $this->buildEndpoint($licenseId) . '/usage';
        $response = $this->httpClient->get($endpoint);
        
        return json_decode($response->getBody()->getContents(), true);
    }
    
    /**
     * Validate a license key.
     */
    public function validateKey(string $licenseKey)
    {
        $endpoint = 'licenses/validate';
        $response = $this->httpClient->post($endpoint, ['license_key' => $licenseKey]);
        
        $data = json_decode($response->getBody()->getContents(), true);
        return $data['is_valid'] ?? false;
    }
    
    protected function validateCreateData(array $data): void
    {
        $required = ['user_id', 'plan_id'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new ValidationException("Field '{$field}' is required for creating a license");
            }
        }
        
        // Validate numeric fields
        if (!is_numeric($data['user_id'])) {
            throw new ValidationException("User ID must be numeric");
        }
        
        if (!is_numeric($data['plan_id'])) {
            throw new ValidationException("Plan ID must be numeric");
        }
    }
    
    protected function validateUpdateData(array $data): void
    {
        // Validate numeric fields if provided
        if (isset($data['user_id']) && !is_numeric($data['user_id'])) {
            throw new ValidationException("User ID must be numeric");
        }
        
        if (isset($data['plan_id']) && !is_numeric($data['plan_id'])) {
            throw new ValidationException("Plan ID must be numeric");
        }
        
        // Validate status if provided
        if (isset($data['status'])) {
            $validStatuses = ['active', 'expired', 'cancelled', 'blocked'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new ValidationException("Invalid status. Must be one of: " . implode(', ', $validStatuses));
            }
        }
    }
}